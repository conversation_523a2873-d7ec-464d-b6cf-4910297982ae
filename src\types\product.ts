
export interface ProductImage {
  id?: string;
  url: string;
  is_primary: boolean;
  alt_text?: string;
  file_name?: string;
  file_size?: number;
  uploaded_at?: string;
}

export interface Product {
  id: string;
  name: string;
  description?: string;
  price: number;
  stock_quantity: number;
  category: string;
  image_url?: string;
  file_url?: string;
  product_images?: ProductImage[];
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface CreateProductData {
  name: string;
  description?: string;
  price: number;
  stock_quantity?: number;
  category: string;
  image_url?: string;
  file_url?: string;
  is_active?: boolean;
}

export interface UpdateProductData extends Partial<CreateProductData> {
  updated_at?: string;
}

export type ProductCategory =
  | 'Digital Templates'
  | 'Print Products'
  | 'Accessories'
  | 'Software'
  | 'Services'
  | 'PDF'
  | 'Book'
  | 'Package'
  | 'Course'
  | 'Ebook'
  | 'Template'
  | 'Tool'
  | 'Electronics';
