
import React, { createContext, useContext, useEffect, useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { cartService } from '@/services/cart';
import { useToast } from '@/hooks/use-toast';

export interface CartItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
  image_url?: string;
}

interface CartContextType {
  items: CartItem[];
  addToCart: (product: Omit<CartItem, 'quantity'>) => Promise<void>;
  removeFromCart: (productId: string) => Promise<void>;
  updateQuantity: (productId: string, quantity: number) => Promise<void>;
  clearCart: () => Promise<void>;
  totalItems: number;
  totalPrice: number;
  loading: boolean;
}

const CartContext = createContext<CartContextType | undefined>(undefined);

export function useCart() {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
}

export function CartProvider({ children }: { children: React.ReactNode }) {
  const [items, setItems] = useState<CartItem[]>([]);
  const [loading, setLoading] = useState(false);
  const { user } = useAuth();
  const { toast } = useToast();

  // Load cart from Supabase when user is authenticated
  useEffect(() => {
    if (user) {
      loadCart();
    } else {
      // Load from localStorage for non-authenticated users
      const savedCart = localStorage.getItem('enzoshop-cart');
      if (savedCart) {
        setItems(JSON.parse(savedCart));
      }
    }
  }, [user]);

  // Save to localStorage for non-authenticated users
  useEffect(() => {
    if (!user) {
      localStorage.setItem('enzoshop-cart', JSON.stringify(items));
    }
  }, [items, user]);

  const loadCart = async () => {
    if (!user) return;
    
    try {
      setLoading(true);
      const { data, error } = await cartService.getCart(user.id);
      if (error) throw error;
      
      const cartItems = data?.map((item: any) => ({
        id: item.products.id,
        name: item.products.name,
        price: item.products.price,
        quantity: item.quantity,
        image_url: item.products.image_url,
      })) || [];
      
      setItems(cartItems);
    } catch (error) {
      console.error('Error loading cart:', error);
    } finally {
      setLoading(false);
    }
  };

  const addToCart = async (product: Omit<CartItem, 'quantity'>) => {
    if (user) {
      try {
        const { error } = await cartService.addToCart(user.id, product.id, 1);
        if (error) throw error;
        await loadCart();
        toast({
          title: "Added to cart",
          description: `${product.name} added to your cart`,
        });
      } catch (error) {
        console.error('Error adding to cart:', error);
        toast({
          title: "Error",
          description: "Failed to add item to cart",
          variant: "destructive",
        });
      }
    } else {
      // Local storage logic for non-authenticated users
      setItems(current => {
        const existingItem = current.find(item => item.id === product.id);
        
        if (existingItem) {
          toast({
            title: "Added to cart",
            description: `${product.name} quantity increased`,
          });
          return current.map(item =>
            item.id === product.id
              ? { ...item, quantity: item.quantity + 1 }
              : item
          );
        } else {
          toast({
            title: "Added to cart",
            description: `${product.name} added to your cart`,
          });
          return [...current, { ...product, quantity: 1 }];
        }
      });
    }
  };

  const removeFromCart = async (productId: string) => {
    if (user) {
      try {
        const { error } = await cartService.removeFromCart(user.id, productId);
        if (error) throw error;
        await loadCart();
        toast({
          title: "Removed from cart",
          description: "Item removed from your cart",
        });
      } catch (error) {
        console.error('Error removing from cart:', error);
        toast({
          title: "Error",
          description: "Failed to remove item from cart",
          variant: "destructive",
        });
      }
    } else {
      setItems(current => {
        const item = current.find(item => item.id === productId);
        if (item) {
          toast({
            title: "Removed from cart",
            description: `${item.name} removed from your cart`,
          });
        }
        return current.filter(item => item.id !== productId);
      });
    }
  };

  const updateQuantity = async (productId: string, quantity: number) => {
    if (quantity <= 0) {
      await removeFromCart(productId);
      return;
    }

    if (user) {
      try {
        const { error } = await cartService.updateCartItem(user.id, productId, quantity);
        if (error) throw error;
        await loadCart();
      } catch (error) {
        console.error('Error updating cart:', error);
      }
    } else {
      setItems(current =>
        current.map(item =>
          item.id === productId ? { ...item, quantity } : item
        )
      );
    }
  };

  const clearCart = async () => {
    if (user) {
      try {
        const { error } = await cartService.clearCart(user.id);
        if (error) throw error;
        setItems([]);
        toast({
          title: "Cart cleared",
          description: "All items removed from your cart",
        });
      } catch (error) {
        console.error('Error clearing cart:', error);
      }
    } else {
      setItems([]);
      toast({
        title: "Cart cleared",
        description: "All items removed from your cart",
      });
    }
  };

  const totalItems = items.reduce((sum, item) => sum + item.quantity, 0);
  const totalPrice = items.reduce((sum, item) => sum + item.price * item.quantity, 0);

  const value = {
    items,
    addToCart,
    removeFromCart,
    updateQuantity,
    clearCart,
    totalItems,
    totalPrice,
    loading,
  };

  return <CartContext.Provider value={value}>{children}</CartContext.Provider>;
}
